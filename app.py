from flask import Flask, render_template, request, jsonify, session
import os
from game_logic import <PERSON><PERSON>uessGame
from database import GameDatabase

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Initialize database
db = GameDatabase()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/game')
def game():
    """Game page"""
    return render_template('game.html')

@app.route('/leaderboard')
def leaderboard():
    """Leaderboard page"""
    scores = db.get_leaderboard(20)
    stats = db.get_game_stats()
    return render_template('leaderboard.html', scores=scores, stats=stats)

@app.route('/api/start_game', methods=['POST'])
def start_game():
    """Start a new game"""
    data = request.get_json()
    category = data.get('category', None)
    
    # Create new game instance
    game = WordGuessGame()
    game_state = game.start_new_game(category)
    
    # Store game in session
    session['game'] = {
        'current_word': game.current_word,
        'guessed_letters': list(game.guessed_letters),
        'wrong_letters': list(game.wrong_letters),
        'category': game.category,
        'start_time': game.start_time,
        'hints_used': game.hints_used,
        'game_over': game.game_over,
        'game_won': game.game_won
    }
    
    return jsonify({
        'success': True,
        'game_state': game_state
    })

@app.route('/api/guess_letter', methods=['POST'])
def guess_letter():
    """Process letter guess"""
    data = request.get_json()
    letter = data.get('letter', '').upper()
    
    if 'game' not in session:
        return jsonify({'success': False, 'message': 'No active game'})
    
    # Restore game from session
    game = WordGuessGame()
    game_data = session['game']
    game.current_word = game_data['current_word']
    game.guessed_letters = set(game_data['guessed_letters'])
    game.wrong_letters = set(game_data['wrong_letters'])
    game.category = game_data['category']
    game.start_time = game_data['start_time']
    game.hints_used = game_data['hints_used']
    game.game_over = game_data['game_over']
    game.game_won = game_data['game_won']
    
    # Process guess
    result = game.guess_letter(letter)
    
    # Update session
    session['game'] = {
        'current_word': game.current_word,
        'guessed_letters': list(game.guessed_letters),
        'wrong_letters': list(game.wrong_letters),
        'category': game.category,
        'start_time': game.start_time,
        'hints_used': game.hints_used,
        'game_over': game.game_over,
        'game_won': game.game_won
    }
    
    # If game is over, calculate score and update stats
    if game.game_over:
        game.end_time = result.get('end_time', game.start_time + game.get_time_taken())
        score = game.calculate_score()
        time_taken = game.get_time_taken()
        
        # Update database
        db.update_game_stats(game.game_won)
        
        result['score'] = score
        result['time_taken'] = time_taken
    
    return jsonify({
        'success': True,
        'result': result
    })

@app.route('/api/get_hint', methods=['POST'])
def get_hint():
    """Get a hint"""
    if 'game' not in session:
        return jsonify({'success': False, 'message': 'No active game'})
    
    # Restore game from session
    game = WordGuessGame()
    game_data = session['game']
    game.current_word = game_data['current_word']
    game.guessed_letters = set(game_data['guessed_letters'])
    game.wrong_letters = set(game_data['wrong_letters'])
    game.category = game_data['category']
    game.start_time = game_data['start_time']
    game.hints_used = game_data['hints_used']
    game.game_over = game_data['game_over']
    game.game_won = game_data['game_won']
    
    # Get hint
    result = game.get_hint()
    
    # Update session
    session['game'] = {
        'current_word': game.current_word,
        'guessed_letters': list(game.guessed_letters),
        'wrong_letters': list(game.wrong_letters),
        'category': game.category,
        'start_time': game.start_time,
        'hints_used': game.hints_used,
        'game_over': game.game_over,
        'game_won': game.game_won
    }
    
    return jsonify({
        'success': True,
        'result': result
    })

@app.route('/api/save_score', methods=['POST'])
def save_score():
    """Save player score"""
    data = request.get_json()
    player_name = data.get('player_name', 'Anonymous')
    score = data.get('score', 0)
    category = data.get('category', '')
    word = data.get('word', '')
    time_taken = data.get('time_taken', 0)
    
    db.save_score(player_name, score, category, word, time_taken)
    
    return jsonify({'success': True})

@app.route('/api/get_categories')
def get_categories():
    """Get available categories"""
    game = WordGuessGame()
    categories = game.get_available_categories()
    return jsonify({'categories': categories})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
