// Game state variables
let gameState = null;
let gameTimer = null;
let startTime = null;

// DOM elements
const wordDisplay = document.getElementById('wordDisplay');
const categoryDisplay = document.getElementById('categoryDisplay');
const timerDisplay = document.getElementById('timer');
const letterInput = document.getElementById('letterInput');
const correctLetters = document.getElementById('correctLetters');
const wrongLetters = document.getElementById('wrongLetters');
const wrongCount = document.getElementById('wrongCount');
const maxWrong = document.getElementById('maxWrong');
const messageDisplay = document.getElementById('messageDisplay');
const hintBtn = document.getElementById('hintBtn');
const hintsLeft = document.getElementById('hintsLeft');
const gameOverModal = document.getElementById('gameOverModal');

// Hangman parts
const hangmanParts = ['head', 'body', 'leftArm', 'rightArm', 'leftLeg', 'rightLeg'];

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeGame();
    setupEventListeners();
});

function initializeGame() {
    // Check if there's an active game session
    fetch('/api/start_game', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            gameState = data.game_state;
            updateGameDisplay();
            startTimer();
        } else {
            showMessage('Error loading game: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error initializing game:', error);
        showMessage('Error loading game. Please refresh the page.', 'error');
    });
}

function setupEventListeners() {
    // Letter input event listeners
    if (letterInput) {
        letterInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                guessLetter();
            }
        });

        letterInput.addEventListener('input', function(e) {
            // Only allow letters
            this.value = this.value.replace(/[^a-zA-Z]/g, '').toUpperCase();
        });
    }

    // Focus on input when page loads
    if (letterInput) {
        letterInput.focus();
    }
}

function updateGameDisplay() {
    if (!gameState) return;

    // Update word display
    if (wordDisplay) {
        wordDisplay.textContent = gameState.word_display;
    }

    // Update category
    if (categoryDisplay) {
        categoryDisplay.textContent = gameState.category.charAt(0).toUpperCase() + gameState.category.slice(1);
    }

    // Update wrong guesses counter
    if (wrongCount && maxWrong) {
        wrongCount.textContent = gameState.wrong_guesses;
        maxWrong.textContent = gameState.max_wrong_guesses;
    }

    // Update hangman display
    updateHangmanDisplay(gameState.wrong_guesses);

    // Update letters display
    if (correctLetters) {
        correctLetters.textContent = gameState.guessed_letters.length > 0 
            ? gameState.guessed_letters.join(', ') 
            : '-';
    }

    if (wrongLetters) {
        wrongLetters.textContent = gameState.wrong_letters.length > 0 
            ? gameState.wrong_letters.join(', ') 
            : '-';
    }

    // Update hints
    if (hintsLeft) {
        const remaining = gameState.max_hints - gameState.hints_used;
        hintsLeft.textContent = remaining;
        
        if (hintBtn) {
            hintBtn.disabled = remaining <= 0 || gameState.game_over;
        }
    }

    // Check if game is over
    if (gameState.game_over) {
        stopTimer();
        showGameOverModal();
    }

    // Clear input
    if (letterInput) {
        letterInput.value = '';
        letterInput.focus();
    }
}

function updateHangmanDisplay(wrongGuesses) {
    // Hide all parts first
    hangmanParts.forEach(part => {
        const element = document.getElementById(part);
        if (element) {
            element.style.display = 'none';
        }
    });

    // Show parts based on wrong guesses
    for (let i = 0; i < wrongGuesses && i < hangmanParts.length; i++) {
        const element = document.getElementById(hangmanParts[i]);
        if (element) {
            element.style.display = 'block';
        }
    }
}

async function guessLetter() {
    if (!letterInput || !letterInput.value.trim()) {
        showMessage('Masukkan huruf terlebih dahulu!', 'warning');
        return;
    }

    const letter = letterInput.value.trim().toUpperCase();

    try {
        const response = await fetch('/api/guess_letter', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ letter: letter })
        });

        const data = await response.json();

        if (data.success) {
            gameState = data.result;
            updateGameDisplay();
            
            if (data.result.message) {
                showMessage(data.result.message, data.result.message_type);
            }
        } else {
            showMessage('Error: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Error guessing letter:', error);
        showMessage('Error processing guess. Please try again.', 'error');
    }
}

async function getHint() {
    try {
        const response = await fetch('/api/get_hint', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            gameState = data.result;
            updateGameDisplay();
            
            if (data.result.message) {
                showMessage(data.result.message, data.result.message_type);
            }
        } else {
            showMessage('Error: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Error getting hint:', error);
        showMessage('Error getting hint. Please try again.', 'error');
    }
}

function newGame() {
    // Hide modal if open
    if (gameOverModal) {
        gameOverModal.style.display = 'none';
    }

    // Redirect to home page to select new category
    window.location.href = '/';
}

function showMessage(message, type = 'info') {
    if (!messageDisplay) return;

    messageDisplay.textContent = message;
    messageDisplay.className = `message ${type} show`;

    // Hide message after 3 seconds
    setTimeout(() => {
        messageDisplay.classList.remove('show');
    }, 3000);
}

function startTimer() {
    startTime = Date.now();
    
    gameTimer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        
        if (timerDisplay) {
            timerDisplay.textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }, 1000);
}

function stopTimer() {
    if (gameTimer) {
        clearInterval(gameTimer);
        gameTimer = null;
    }
}

function showGameOverModal() {
    if (!gameOverModal || !gameState) return;

    const gameOverTitle = document.getElementById('gameOverTitle');
    const gameOverMessage = document.getElementById('gameOverMessage');
    const finalScore = document.getElementById('finalScore');
    const finalTime = document.getElementById('finalTime');
    const finalWord = document.getElementById('finalWord');

    // Update modal content
    if (gameOverTitle) {
        gameOverTitle.textContent = gameState.game_won ? '🎉 Selamat!' : '😞 Game Over';
    }

    if (gameOverMessage) {
        gameOverMessage.innerHTML = gameState.game_won 
            ? `<p>Anda berhasil menebak kata dengan benar!</p>`
            : `<p>Jangan menyerah! Coba lagi untuk meningkatkan kemampuan Anda.</p>`;
    }

    if (finalScore) {
        finalScore.textContent = gameState.score || 0;
    }

    if (finalTime) {
        finalTime.textContent = gameState.time_taken || 0;
    }

    if (finalWord) {
        finalWord.textContent = gameState.current_word || '';
    }

    // Show modal
    gameOverModal.style.display = 'block';
}

async function saveScore() {
    const playerNameInput = document.getElementById('playerName');
    const playerName = playerNameInput ? playerNameInput.value.trim() : '';

    if (!playerName) {
        showMessage('Masukkan nama Anda terlebih dahulu!', 'warning');
        return;
    }

    if (!gameState || !gameState.game_won) {
        showMessage('Tidak ada skor untuk disimpan.', 'warning');
        return;
    }

    try {
        const response = await fetch('/api/save_score', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                player_name: playerName,
                score: gameState.score || 0,
                category: gameState.category,
                word: gameState.current_word,
                time_taken: gameState.time_taken || 0
            })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('Skor berhasil disimpan!', 'success');
            
            // Disable save button
            const saveBtn = event.target;
            saveBtn.disabled = true;
            saveBtn.textContent = 'Tersimpan ✓';
        } else {
            showMessage('Error menyimpan skor.', 'error');
        }
    } catch (error) {
        console.error('Error saving score:', error);
        showMessage('Error menyimpan skor. Please try again.', 'error');
    }
}

// Close modal when clicking outside
window.addEventListener('click', function(event) {
    if (event.target === gameOverModal) {
        gameOverModal.style.display = 'none';
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // ESC to close modal
    if (e.key === 'Escape' && gameOverModal && gameOverModal.style.display === 'block') {
        gameOverModal.style.display = 'none';
    }
    
    // H for hint
    if (e.key.toLowerCase() === 'h' && !gameState?.game_over) {
        e.preventDefault();
        getHint();
    }
    
    // N for new game
    if (e.key.toLowerCase() === 'n' && e.ctrlKey) {
        e.preventDefault();
        newGame();
    }
});
