# 🎯 Game Tebak Kata Web

Game tebak kata interaktif berbasis web yang dibuat dengan Python Flask. Game ini menampilkan hangman klasik dengan antarmuka modern dan fitur-fitur menarik.

## ✨ Fitur

### 🎮 Gameplay
- **Tebak Kata**: Tebak kata dengan menebak huruf satu per satu
- **Kategori Beragam**: 5 kategori (He<PERSON>, Buah, <PERSON>egara, <PERSON><PERSON><PERSON>, <PERSON>esi)
- **Hangman Visual**: Gambar hangman yang muncul setiap salah tebak
- **Sistem Hint**: Maksimal 2 hint per game
- **Timer**: Waktu bermain dicatat untuk skor

### 🏆 Sistem Skor
- **Skor Dasar**: Panjang kata × 10 poin
- **Bonus Waktu**: Semakin cepat semakin baik
- **Penalti**: -5 poin per huruf salah, -10 poin per hint
- **Leaderboard**: Papan skor dengan top pemain

### 🎨 Antarmuka
- **Responsive Design**: Berfungsi di desktop dan mobile
- **Modern UI**: Desain gradient yang menarik
- **Animasi Smooth**: Transisi dan efek hover
- **Dark/Light Theme**: Kontras yang baik

## 🚀 Instalasi dan Menjalankan

### Prasyarat
- Python 3.7+
- pip (Python package manager)

### Langkah Instalasi

1. **Clone atau download project**
   ```bash
   # Jika menggunakan git
   git clone <repository-url>
   cd game-tebak-kata
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Jalankan aplikasi**
   ```bash
   python app.py
   # atau
   py app.py
   ```

4. **Buka browser**
   ```
   http://127.0.0.1:5000
   ```

## 📁 Struktur Project

```
game-tebak-kata/
├── app.py                 # Main Flask application
├── game_logic.py          # Logic game tebak kata
├── database.py            # Database setup dan fungsi
├── words.json             # Database kata-kata
├── requirements.txt       # Python dependencies
├── templates/
│   ├── index.html         # Halaman utama
│   ├── game.html          # Halaman gameplay
│   └── leaderboard.html   # Halaman papan skor
├── static/
│   ├── style.css          # Styling CSS
│   └── script.js          # JavaScript interaktif
└── README.md              # Dokumentasi
```

## 🎯 Cara Bermain

1. **Pilih Kategori**: Pilih kategori kata atau biarkan random
2. **Mulai Game**: Klik "Mulai Permainan"
3. **Tebak Huruf**: Masukkan huruf satu per satu
4. **Gunakan Hint**: Jika kesulitan, gunakan hint (maksimal 2)
5. **Menang/Kalah**: 
   - Menang: Tebak semua huruf sebelum hangman selesai
   - Kalah: Hangman selesai (6 kesalahan)
6. **Simpan Skor**: Masukkan nama untuk menyimpan skor

## 🔧 Konfigurasi

### Menambah Kata Baru
Edit file `words.json` untuk menambah kata atau kategori baru:

```json
{
  "kategori_baru": [
    "KATA1", "KATA2", "KATA3"
  ]
}
```

### Mengubah Pengaturan Game
Edit file `game_logic.py`:
- `max_wrong_guesses`: Jumlah kesalahan maksimal (default: 6)
- `max_hints`: Jumlah hint maksimal (default: 2)

### Database
Game menggunakan SQLite database (`game.db`) yang dibuat otomatis untuk:
- Menyimpan skor pemain
- Statistik game
- Leaderboard

## 🛠️ Teknologi yang Digunakan

- **Backend**: Flask (Python)
- **Frontend**: HTML5, CSS3, JavaScript
- **Database**: SQLite
- **Styling**: CSS Grid, Flexbox, Gradients
- **Fonts**: Google Fonts (Poppins)

## 📱 Responsive Design

Game ini fully responsive dan berfungsi dengan baik di:
- 💻 Desktop (1200px+)
- 📱 Tablet (768px - 1199px)
- 📱 Mobile (< 768px)

## 🎨 Fitur UI/UX

- **Gradient Background**: Warna biru-ungu yang menarik
- **Card Design**: Layout card dengan shadow dan border radius
- **Hover Effects**: Animasi saat hover button
- **Modal Dialog**: Game over modal dengan animasi
- **Loading States**: Feedback visual untuk aksi user
- **Keyboard Shortcuts**: 
  - Enter: Submit tebakan
  - H: Hint
  - Ctrl+N: Game baru
  - ESC: Tutup modal

## 🔮 Pengembangan Selanjutnya

Ide untuk pengembangan lebih lanjut:
- [ ] Multiplayer mode
- [ ] Difficulty levels
- [ ] Sound effects
- [ ] Achievement system
- [ ] Social sharing
- [ ] User accounts
- [ ] Custom word lists
- [ ] Tournament mode

## 📄 Lisensi

Project ini dibuat untuk tujuan edukasi dan dapat digunakan secara bebas.

## 🤝 Kontribusi

Kontribusi sangat diterima! Silakan:
1. Fork project
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Buat Pull Request

## 📞 Support

Jika ada pertanyaan atau masalah, silakan buat issue di repository ini.

---

**Selamat bermain! 🎮**
