<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Tebak Kata</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎯 Game Tebak Kata</h1>
            <p>Uji kemampuan kosakata Anda dengan game tebak kata yang seru!</p>
        </header>

        <main>
            <div class="menu-card">
                <h2>Pilih Mode Permainan</h2>
                
                <div class="category-selection">
                    <h3>Pilih Kategori:</h3>
                    <div class="category-grid" id="categoryGrid">
                        <!-- Categories will be loaded here -->
                    </div>
                    <button class="category-btn random-category" onclick="startGame()">
                        🎲 Kategori Random
                    </button>
                </div>

                <div class="game-actions">
                    <button class="btn btn-primary" onclick="startGame()">
                        🎮 Mulai Permainan
                    </button>
                    <a href="/leaderboard" class="btn btn-secondary">
                        🏆 Papan Skor
                    </a>
                </div>
            </div>

            <div class="info-cards">
                <div class="info-card">
                    <h3>📝 Cara Bermain</h3>
                    <ul>
                        <li>Pilih kategori atau biarkan random</li>
                        <li>Tebak huruf satu per satu</li>
                        <li>Anda memiliki 6 kesempatan salah</li>
                        <li>Gunakan hint jika diperlukan (maksimal 2)</li>
                        <li>Selesaikan kata untuk mendapat skor!</li>
                    </ul>
                </div>

                <div class="info-card">
                    <h3>🎯 Sistem Skor</h3>
                    <ul>
                        <li>Skor dasar: Panjang kata × 10</li>
                        <li>Bonus waktu: Semakin cepat semakin baik</li>
                        <li>Penalti: -5 per huruf salah</li>
                        <li>Penalti hint: -10 per hint</li>
                    </ul>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2024 Game Tebak Kata - Dibuat dengan ❤️</p>
        </footer>
    </div>

    <script>
        let selectedCategory = null;

        // Load categories when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
        });

        async function loadCategories() {
            try {
                const response = await fetch('/api/get_categories');
                const data = await response.json();
                
                const categoryGrid = document.getElementById('categoryGrid');
                categoryGrid.innerHTML = '';
                
                data.categories.forEach(category => {
                    const button = document.createElement('button');
                    button.className = 'category-btn';
                    button.textContent = category.charAt(0).toUpperCase() + category.slice(1);
                    button.onclick = () => selectCategory(category, button);
                    categoryGrid.appendChild(button);
                });
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        function selectCategory(category, buttonElement) {
            // Remove active class from all buttons
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to selected button
            buttonElement.classList.add('active');
            selectedCategory = category;
        }

        async function startGame() {
            try {
                const response = await fetch('/api/start_game', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        category: selectedCategory
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Redirect to game page
                    window.location.href = '/game';
                } else {
                    alert('Error starting game: ' + data.message);
                }
            } catch (error) {
                console.error('Error starting game:', error);
                alert('Error starting game. Please try again.');
            }
        }

        // Add some interactive effects
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
