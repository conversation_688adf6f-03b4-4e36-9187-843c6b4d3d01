import random
import time
from database import load_words

class WordGuessGame:
    def __init__(self):
        self.words_data = load_words()
        self.reset_game()
    
    def reset_game(self):
        """Reset game state"""
        self.current_word = ""
        self.guessed_letters = set()
        self.wrong_letters = set()
        self.category = ""
        self.max_wrong_guesses = 6
        self.start_time = None
        self.end_time = None
        self.game_won = False
        self.game_over = False
        self.hints_used = 0
        self.max_hints = 2
    
    def start_new_game(self, category=None):
        """Start a new game with optional category"""
        self.reset_game()
        
        if category and category in self.words_data:
            self.category = category
        else:
            self.category = random.choice(list(self.words_data.keys()))
        
        self.current_word = random.choice(self.words_data[self.category])
        self.start_time = time.time()
        
        return {
            'word_display': self.get_word_display(),
            'category': self.category,
            'wrong_guesses': len(self.wrong_letters),
            'max_wrong_guesses': self.max_wrong_guesses,
            'guessed_letters': list(self.guessed_letters),
            'wrong_letters': list(self.wrong_letters),
            'hints_used': self.hints_used,
            'max_hints': self.max_hints
        }
    
    def get_word_display(self):
        """Get current word display with guessed letters"""
        display = ""
        for letter in self.current_word:
            if letter in self.guessed_letters:
                display += letter + " "
            else:
                display += "_ "
        return display.strip()
    
    def guess_letter(self, letter):
        """Process a letter guess"""
        if self.game_over:
            return self.get_game_state()
        
        letter = letter.upper()
        
        # Check if letter already guessed
        if letter in self.guessed_letters or letter in self.wrong_letters:
            return {
                **self.get_game_state(),
                'message': f'Huruf {letter} sudah ditebak sebelumnya!',
                'message_type': 'warning'
            }
        
        # Check if letter is in word
        if letter in self.current_word:
            self.guessed_letters.add(letter)
            message = f'Bagus! Huruf {letter} ada dalam kata.'
            message_type = 'success'
            
            # Check if word is complete
            if all(letter in self.guessed_letters for letter in self.current_word):
                self.game_won = True
                self.game_over = True
                self.end_time = time.time()
                message = f'Selamat! Anda berhasil menebak kata "{self.current_word}"!'
                message_type = 'success'
        else:
            self.wrong_letters.add(letter)
            message = f'Maaf, huruf {letter} tidak ada dalam kata.'
            message_type = 'error'
            
            # Check if game over
            if len(self.wrong_letters) >= self.max_wrong_guesses:
                self.game_over = True
                self.end_time = time.time()
                message = f'Game Over! Kata yang benar adalah "{self.current_word}".'
                message_type = 'error'
        
        return {
            **self.get_game_state(),
            'message': message,
            'message_type': message_type
        }
    
    def get_hint(self):
        """Get a hint for the current word"""
        if self.hints_used >= self.max_hints:
            return {
                **self.get_game_state(),
                'message': 'Anda sudah menggunakan semua hint!',
                'message_type': 'warning'
            }
        
        # Find unguessed letters
        unguessed_letters = [letter for letter in self.current_word 
                           if letter not in self.guessed_letters]
        
        if unguessed_letters:
            hint_letter = random.choice(unguessed_letters)
            self.guessed_letters.add(hint_letter)
            self.hints_used += 1
            
            # Check if word is complete after hint
            if all(letter in self.guessed_letters for letter in self.current_word):
                self.game_won = True
                self.game_over = True
                self.end_time = time.time()
                message = f'Hint: Huruf {hint_letter}. Selamat! Anda menyelesaikan kata!'
            else:
                message = f'Hint: Huruf {hint_letter} ada dalam kata.'
            
            return {
                **self.get_game_state(),
                'message': message,
                'message_type': 'info'
            }
        
        return {
            **self.get_game_state(),
            'message': 'Tidak ada hint yang tersedia.',
            'message_type': 'warning'
        }
    
    def get_game_state(self):
        """Get current game state"""
        return {
            'word_display': self.get_word_display(),
            'category': self.category,
            'wrong_guesses': len(self.wrong_letters),
            'max_wrong_guesses': self.max_wrong_guesses,
            'guessed_letters': sorted(list(self.guessed_letters)),
            'wrong_letters': sorted(list(self.wrong_letters)),
            'game_over': self.game_over,
            'game_won': self.game_won,
            'current_word': self.current_word if self.game_over else None,
            'hints_used': self.hints_used,
            'max_hints': self.max_hints
        }
    
    def calculate_score(self):
        """Calculate final score based on performance"""
        if not self.game_won or not self.end_time:
            return 0
        
        time_taken = int(self.end_time - self.start_time)
        base_score = len(self.current_word) * 10
        
        # Bonus for fewer wrong guesses
        wrong_penalty = len(self.wrong_letters) * 5
        
        # Penalty for hints used
        hint_penalty = self.hints_used * 10
        
        # Time bonus (faster = better)
        time_bonus = max(0, 60 - time_taken)
        
        final_score = max(0, base_score - wrong_penalty - hint_penalty + time_bonus)
        
        return final_score
    
    def get_time_taken(self):
        """Get time taken for current game"""
        if self.start_time and self.end_time:
            return int(self.end_time - self.start_time)
        elif self.start_time:
            return int(time.time() - self.start_time)
        return 0
    
    def get_available_categories(self):
        """Get list of available categories"""
        return list(self.words_data.keys())
