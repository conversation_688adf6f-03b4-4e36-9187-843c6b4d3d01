<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Tebak Kata - Papan Skor</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1>🏆 Papan Skor</h1>
            <p>Pemain terbaik dalam Game Tebak Kata</p>
        </header>

        <main>
            <!-- Game Statistics -->
            <div class="stats-container">
                <h2>📊 Statistik Game</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.total_games }}</div>
                        <div class="stat-label">Total Game</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.total_wins }}</div>
                        <div class="stat-label">Menang</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.total_losses }}</div>
                        <div class="stat-label">Kalah</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.win_rate }}%</div>
                        <div class="stat-label">Win Rate</div>
                    </div>
                </div>
            </div>

            <!-- Leaderboard -->
            <div class="leaderboard-container">
                <h2>🥇 Top Pemain</h2>
                
                {% if scores %}
                <div class="leaderboard-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Nama</th>
                                <th>Skor</th>
                                <th>Kategori</th>
                                <th>Kata</th>
                                <th>Waktu</th>
                                <th>Tanggal</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for score in scores %}
                            <tr class="{% if loop.index <= 3 %}top-{{ loop.index }}{% endif %}">
                                <td class="rank">
                                    {% if loop.index == 1 %}🥇
                                    {% elif loop.index == 2 %}🥈
                                    {% elif loop.index == 3 %}🥉
                                    {% else %}{{ loop.index }}
                                    {% endif %}
                                </td>
                                <td class="player-name">{{ score[0] }}</td>
                                <td class="score">{{ score[1] }}</td>
                                <td class="category">{{ score[2] }}</td>
                                <td class="word">{{ score[3] }}</td>
                                <td class="time">{{ score[4] }}s</td>
                                <td class="date">{{ score[5][:10] }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="no-scores">
                    <p>Belum ada skor yang tersimpan.</p>
                    <p>Jadilah yang pertama bermain!</p>
                </div>
                {% endif %}
            </div>

            <!-- Actions -->
            <div class="leaderboard-actions">
                <a href="/" class="btn btn-primary">🎮 Main Sekarang</a>
                <button onclick="location.reload()" class="btn btn-secondary">🔄 Refresh</button>
            </div>
        </main>

        <footer>
            <p>&copy; 2024 Game Tebak Kata - Dibuat dengan ❤️</p>
        </footer>
    </div>

    <script>
        // Add some interactive effects
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Highlight top 3 players
        document.querySelectorAll('.top-1, .top-2, .top-3').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Auto refresh every 30 seconds
        setInterval(() => {
            const lastRefresh = localStorage.getItem('lastRefresh');
            const now = Date.now();
            
            if (!lastRefresh || now - parseInt(lastRefresh) > 30000) {
                localStorage.setItem('lastRefresh', now.toString());
                // Uncomment the line below if you want auto-refresh
                // location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
