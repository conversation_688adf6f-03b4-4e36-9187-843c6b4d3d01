import sqlite3
import json
from datetime import datetime

class GameDatabase:
    def __init__(self, db_name='game.db'):
        self.db_name = db_name
        self.init_database()
        
    def init_database(self):
        """Initialize database tables"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Create scores table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scores (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_name TEXT NOT NULL,
                score INTEGER NOT NULL,
                category TEXT NOT NULL,
                word TEXT NOT NULL,
                time_taken INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create game_stats table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS game_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                total_games INTEGER DEFAULT 0,
                total_wins INTEGER DEFAULT 0,
                total_losses INTEGER DEFAULT 0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def save_score(self, player_name, score, category, word, time_taken):
        """Save player score to database"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO scores (player_name, score, category, word, time_taken)
            VALUES (?, ?, ?, ?, ?)
        ''', (player_name, score, category, word, time_taken))
        
        conn.commit()
        conn.close()
    
    def get_leaderboard(self, limit=10):
        """Get top scores from database"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT player_name, score, category, word, time_taken, created_at
            FROM scores
            ORDER BY score DESC, time_taken ASC
            LIMIT ?
        ''', (limit,))
        
        results = cursor.fetchall()
        conn.close()
        
        return results
    
    def update_game_stats(self, won=False):
        """Update game statistics"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Check if stats exist
        cursor.execute('SELECT * FROM game_stats LIMIT 1')
        stats = cursor.fetchone()
        
        if stats:
            if won:
                cursor.execute('''
                    UPDATE game_stats 
                    SET total_games = total_games + 1, 
                        total_wins = total_wins + 1,
                        updated_at = CURRENT_TIMESTAMP
                ''')
            else:
                cursor.execute('''
                    UPDATE game_stats 
                    SET total_games = total_games + 1, 
                        total_losses = total_losses + 1,
                        updated_at = CURRENT_TIMESTAMP
                ''')
        else:
            # Create initial stats
            if won:
                cursor.execute('''
                    INSERT INTO game_stats (total_games, total_wins, total_losses)
                    VALUES (1, 1, 0)
                ''')
            else:
                cursor.execute('''
                    INSERT INTO game_stats (total_games, total_wins, total_losses)
                    VALUES (1, 0, 1)
                ''')
        
        conn.commit()
        conn.close()
    
    def get_game_stats(self):
        """Get game statistics"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM game_stats LIMIT 1')
        stats = cursor.fetchone()
        conn.close()
        
        if stats:
            return {
                'total_games': stats[1],
                'total_wins': stats[2],
                'total_losses': stats[3],
                'win_rate': round((stats[2] / stats[1]) * 100, 1) if stats[1] > 0 else 0
            }
        else:
            return {
                'total_games': 0,
                'total_wins': 0,
                'total_losses': 0,
                'win_rate': 0
            }

def load_words():
    """Load words from JSON file"""
    try:
        with open('words.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {
            "hewan": ["KUCING", "ANJING", "GAJAH"],
            "buah": ["APEL", "JERUK", "PISANG"],
            "negara": ["INDONESIA", "MALAYSIA", "SINGAPURA"]
        }
