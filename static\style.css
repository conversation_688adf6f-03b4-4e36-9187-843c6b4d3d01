/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    background: rgba(255,255,255,0.1);
    padding: 10px 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.category, .timer {
    font-weight: 600;
    font-size: 1.1rem;
}

/* Main Content */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.game-main {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 20px;
}

/* Cards */
.menu-card, .info-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 20px;
}

.menu-card h2 {
    text-align: center;
    color: #667eea;
    margin-bottom: 30px;
    font-size: 2rem;
}

/* Category Selection */
.category-selection {
    margin-bottom: 30px;
}

.category-selection h3 {
    margin-bottom: 15px;
    color: #333;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.category-btn {
    padding: 15px 20px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: capitalize;
}

.category-btn:hover, .category-btn.active {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.random-category {
    grid-column: 1 / -1;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
}

/* Buttons */
.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #dee2e6;
}

.btn-hint {
    background: linear-gradient(45deg, #ffeaa7, #fdcb6e);
    color: #333;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.game-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Info Cards */
.info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-card h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.info-card ul {
    list-style: none;
}

.info-card li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.info-card li:before {
    content: "✓ ";
    color: #667eea;
    font-weight: bold;
}

/* Hangman Display */
.hangman-container {
    text-align: center;
    margin-bottom: 30px;
}

.hangman-display {
    width: 200px;
    height: 250px;
    margin: 0 auto 20px;
    position: relative;
}

.gallows {
    position: absolute;
    left: 50px;
    top: 20px;
}

.base {
    width: 100px;
    height: 10px;
    background: #8B4513;
    position: absolute;
    bottom: -230px;
}

.pole {
    width: 10px;
    height: 200px;
    background: #8B4513;
    position: absolute;
    left: 10px;
    bottom: -230px;
}

.top {
    width: 80px;
    height: 10px;
    background: #8B4513;
    position: absolute;
    left: 10px;
    bottom: -40px;
}

.noose {
    width: 3px;
    height: 30px;
    background: #654321;
    position: absolute;
    left: 70px;
    bottom: -40px;
}

.hangman-body {
    position: absolute;
    left: 60px;
    top: 50px;
}

.head, .body, .left-arm, .right-arm, .left-leg, .right-leg {
    position: absolute;
    display: none;
}

.head {
    width: 20px;
    height: 20px;
    border: 3px solid #333;
    border-radius: 50%;
    left: 5px;
}

.body {
    width: 3px;
    height: 40px;
    background: #333;
    left: 13px;
    top: 20px;
}

.left-arm, .right-arm {
    width: 20px;
    height: 3px;
    background: #333;
    top: 30px;
}

.left-arm {
    left: -5px;
    transform: rotate(-30deg);
}

.right-arm {
    left: 10px;
    transform: rotate(30deg);
}

.left-leg, .right-leg {
    width: 20px;
    height: 3px;
    background: #333;
    top: 55px;
}

.left-leg {
    left: -5px;
    transform: rotate(-30deg);
}

.right-leg {
    left: 10px;
    transform: rotate(30deg);
}

.wrong-counter {
    font-size: 1.2rem;
    font-weight: 600;
    color: #e74c3c;
}

/* Word Display */
.word-container {
    text-align: center;
    margin-bottom: 30px;
}

.word-display {
    font-size: 3rem;
    font-weight: 700;
    color: #667eea;
    letter-spacing: 10px;
    margin-bottom: 20px;
    font-family: 'Courier New', monospace;
}

/* Game Controls */
.game-controls {
    text-align: center;
    margin-bottom: 30px;
}

.letter-input {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.letter-input input {
    padding: 15px;
    font-size: 1.5rem;
    border: 2px solid #667eea;
    border-radius: 10px;
    text-align: center;
    width: 60px;
    text-transform: uppercase;
}

.letter-input input:focus {
    outline: none;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

/* Letters Display */
.letters-container {
    margin-bottom: 30px;
}

.guessed-letters h4 {
    text-align: center;
    margin-bottom: 15px;
    color: #333;
}

.letters-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    max-width: 600px;
    margin: 0 auto;
}

.correct-letters, .wrong-letters {
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.correct-letters {
    background: #d4edda;
    border: 2px solid #28a745;
}

.wrong-letters {
    background: #f8d7da;
    border: 2px solid #dc3545;
}

.label {
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

/* Message Display */
.message-container {
    text-align: center;
    margin-bottom: 20px;
}

.message {
    padding: 15px;
    border-radius: 10px;
    font-weight: 600;
    margin: 10px 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.message.show {
    opacity: 1;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 2px solid #28a745;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 2px solid #dc3545;
}

.message.warning {
    background: #fff3cd;
    color: #856404;
    border: 2px solid #ffc107;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 2px solid #17a2b8;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-content h2 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 2rem;
}

.score-display {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
}

.score-item {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    font-size: 1.1rem;
}

.score-item .label {
    font-weight: 600;
    color: #333;
}

.player-name-input {
    margin: 20px 0;
}

.player-name-input input {
    padding: 15px;
    border: 2px solid #667eea;
    border-radius: 10px;
    width: 100%;
    margin-bottom: 15px;
    font-size: 1rem;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Leaderboard Styles */
.stats-container {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.stats-container h2 {
    text-align: center;
    color: #667eea;
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.leaderboard-container {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.leaderboard-container h2 {
    text-align: center;
    color: #667eea;
    margin-bottom: 20px;
}

.leaderboard-table {
    overflow-x: auto;
}

.leaderboard-table table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.leaderboard-table th,
.leaderboard-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.leaderboard-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.leaderboard-table tr:hover {
    background: #f8f9fa;
}

.top-1 {
    background: linear-gradient(45deg, #ffd700, #ffed4e) !important;
    color: #333 !important;
}

.top-2 {
    background: linear-gradient(45deg, #c0c0c0, #e8e8e8) !important;
    color: #333 !important;
}

.top-3 {
    background: linear-gradient(45deg, #cd7f32, #daa520) !important;
    color: #333 !important;
}

.rank {
    font-weight: 700;
    font-size: 1.2rem;
    text-align: center;
}

.player-name {
    font-weight: 600;
    color: #667eea;
}

.score {
    font-weight: 700;
    color: #28a745;
}

.no-scores {
    text-align: center;
    padding: 40px;
    color: #666;
}

.leaderboard-actions {
    text-align: center;
    margin-top: 20px;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .game-info {
        flex-direction: column;
        gap: 10px;
    }

    .word-display {
        font-size: 2rem;
        letter-spacing: 5px;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }

    .game-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .letters-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }

    .modal-actions {
        flex-direction: column;
    }

    .leaderboard-table {
        font-size: 0.9rem;
    }

    .leaderboard-table th,
    .leaderboard-table td {
        padding: 10px 5px;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.5rem;
    }

    .word-display {
        font-size: 1.5rem;
        letter-spacing: 3px;
    }

    .hangman-display {
        width: 150px;
        height: 200px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-number {
        font-size: 2rem;
    }
}
